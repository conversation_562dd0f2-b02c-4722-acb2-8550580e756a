import { Gith<PERSON> } from "lucide-react"
import { But<PERSON> } from "@/components/ui/button"
import { Card, CardContent, CardDescription, CardFooter, CardHeader, CardTitle } from "@/components/ui/card"
import { signIn } from "@/auth"

async function loginAction() {
    'use server'
    await signIn('github')
}

export default async function LoginPage() {
  return (
    <div className="flex min-h-screen items-center justify-center bg-muted/40 px-4 py-12">
      <Card className="mx-auto max-w-md w-full">
        <CardHeader className="space-y-1 text-center">
          <CardTitle className="text-2xl font-bold">登录</CardTitle>
          <CardDescription></CardDescription>
        </CardHeader>
        <CardContent className="space-y-4">
          <div className="flex justify-center">
            <form action={loginAction}>
                <Button type="submit" className="relative hover:cursor-pointer">
                    <Github className="mr-2 h-4 w-4" />
                    使用 GitHub 登录
                </Button>
            </form>
          </div>
        </CardContent>
        <CardFooter className="flex flex-col space-y-2 text-center text-sm text-muted-foreground">
          <div>还没有账户？GitHub 登录将自动为您创建账户。</div>
        </CardFooter>
      </Card>
    </div>
  )
}

