import Link from "next/link"
import { But<PERSON> } from "./ui/button"


export default function Nav(){

    return <>
    <div className="flex items-center justify-between">
          <Link href="/" className="text-xl font-bold tracking-tighter">
            Cline<span className="text-blue-800">Coder</span>
          </Link>
          <nav className="hidden md:flex items-center space-x-6 text-sm">
            <Link href="/" className="text-gray-800 hover:text-gray-600 transition-colors">
              Home
            </Link>
            <Link href="/articles/" className="text-gray-800 transition-colors ">
              Articles
            </Link>
            <Link href="/topics/" className="text-gray-800 hover:text-gray-600 transition-colors">
              Topics
            </Link>
            <Link href="/about/" className="text-gray-800 hover:text-gray-600 transition-colors">
              About
            </Link>
          </nav>
          <Button
            variant="outline"
            className="border-blue-800 text-blue-800 hover:bg-purple-950 hover:text-white"
          >
            Github
          </Button>
        </div>
    </>
}